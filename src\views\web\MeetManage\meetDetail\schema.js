import moment from 'moment'

export default () => ({
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      type: 'object',
      'x-component-props': {
        autoRow: true,
        grid: true,
        columns: 6,
        labelAlign: 'left',
        labelCol: 24,
        full: true,
      },
      properties: {
        name: {
          type: 'string',
          title: '会议名称',
          required: true,
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          },
        },
        operatorList: {
          type: 'array',
          key: 'operatorList',
          name: 'operatorList',
          title: '经办人',
          required: true,
          'x-component': 'userSelect',
          'x-component-props': {
            placeholder: '请选择经办人',
            maxCount: 10,
            defaultChooseSelf: false,
            editable: true,
          },
          'x-mega-props': {
            span: 2,
          },
        },
        startTime: {
          type: 'string',
          title: '会议日期',
          required: true,
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'DatePicker',
          'x-component-props': {
            placeholder: '请选择',
          },
          default: moment().format('YYYY-MM-DD'),
        },
        meetingRoom: {
          type: 'string',
          title: '会议室',
          required: true,
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          },
        },
        leaderList: {
          type: 'array',
          key: 'leaderList',
          name: 'leaderList',
          title: '参会领导',
          required: true,
          'x-component': 'userSelect',
          'x-component-props': {
            placeholder: '请选择参会领导',
            maxCount: 20,
            defaultChooseSelf: false,
            editable: true,
          },
          'x-mega-props': {
            span: 2,
          },
        },
        meetingType: {
          key: 'meetingType',
          name: 'meetingType',
          title: '会议类型',
          required: true,
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          },
          'x-mega-props': {
            span: 2,
          },
        },
      },
    },
    topicsList: {
      type: 'array',
      'x-component': 'AgendaArrayField',
      'x-component-props': {
        title: '议题列表',
      },
      items: {
        type: 'object',
        properties: {
          reportList: {
            type: 'array',
            title: '汇报人',
            required: true,
            'x-component': 'userSelect',
            'x-component-props': {
              placeholder: '请选择汇报人',
              maxCount: 5,
              defaultChooseSelf: false,
            },
          },
          topicsStartTime: {
            type: 'string',
            title: '开始时间',
            required: true,
            'x-component': 'TimePicker',
            'x-component-props': {
              placeholder: '请选择',
              format: 'HH:mm',
            },
          },
          topicsDuration: {
            type: 'number',
            title: '汇报时长',
            required: true,
            'x-component': 'InputNumber',
            'x-component-props': {
              placeholder: '请输入',
              min: 1,
              max: 300,
              rule: {
                pattern: /^\d+$/, // 只允许输入数字
              },
              addonAfter: '分钟',
            },
          },
          topicsName: {
            type: 'string',
            title: '议题名称',
            required: true,
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            },
          },
          reportDept: {
            type: 'string',
            title: '汇报单位',
            required: true,
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请选择汇报单位',
            },
          },
          attendanceList: {
            type: 'array',
            title: '列席联系人',
            'x-component': 'userSelect',
            'x-component-props': {
              placeholder: '请选择列席联系人',
              maxCount: 10,
              defaultChooseSelf: false,
            },
          },
          attendanceDept: {
            type: 'string',
            title: '列席单位',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请选择列席单位',
            },
          },
        },
      },
    },
  },
})
