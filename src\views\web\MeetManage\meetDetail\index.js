import React, { useEffect, useState, useMemo } from 'react'
import { Spin, message, TimePicker, InputNumber } from 'antd'
import { Input, Select, Radio, FormItemGrid, DatePicker, FormCard, FormBlock, FormMegaLayout } from '@formily/antd-components'
import { SchemaMarkupForm, createAsyncFormActions } from '@formily/antd'
import { useParams, useHistory } from 'react-router-dom'
import SelectDept from 'ROOT/components/Formily/deptSelect'
import UserSelect from 'ROOT/components/Formily/userSelect'
import EditableInput from 'ROOT/components/Formily/Editable'
import { css } from 'emotion'
// import PageHeader from '../components/pageHeader'
import schema from './schema'
import AgendaArrayField from './components/AgendaSection'
import FormActions from './components/FormActions'
import service from 'ROOT/service'
import { numberToChinese } from 'ROOT/utils'
import './index.css'
const MeetManage = () => {
  const { id } = useParams()
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [editable, setEditable] = useState(true)
  const [initValue, setInitValue] = useState({})
  const [isEdit, setIsEdit] = useState(false)
  const actions = useMemo(() => createAsyncFormActions(), [])

  const uesEffects = () => {
    // 可以在这里添加表单联动效果
  }

  // 数据转换函数：将后端数据转换为前端表单格式
  const transformDataForDisplay = (backendData) => {
    // 转换用户数据：从 {uid, name} 转换为 {id, name, ...}
    const transformUserListForDisplay = (userList) => {
      if (!Array.isArray(userList)) return []
      return userList.map(user => ({
        id: user.uid,
        name: user.name,
        // 添加其他必要的字段，如果后端没有提供，可以设置默认值
        orgId: "10032", // 默认值，实际应该从后端获取
        mobile: "", // 默认值
        isUser: true,
        key: `10032-${user.uid}`,
        title: user.name,
        isLeaf: true,
        fullname: `区公司-${user.name}`,
        selectName: `区公司/${user.name}`
      }))
    }

    // 转换部门数据：从 {orgId, orgName, deptId, deptName} 转换为前端格式
    const transformDeptListForDisplay = (deptList) => {
      if (!Array.isArray(deptList)) return []
      return deptList.map(dept => ({
        id: dept.deptId,
        name: dept.deptName,
        orgId: dept.orgId,
        label: dept.deptName,
        value: `_tree${dept.deptId}`,
        isDept: true,
        key: `${dept.orgId}-${dept.deptId}`,
        title: dept.deptName,
        isLeaf: true,
        fullname: `${dept.orgName}-${dept.deptName}`,
        selectName: `${dept.orgName}/${dept.deptName}`
      }))
    }

    // 转换议题列表
    const transformTopicsListForDisplay = (topicsList) => {
      if (!Array.isArray(topicsList)) return []
      return topicsList.map(topic => ({
        topicsName: topic.topicsName,
        topicsStartTime: topic.topicsStartTime ? new Date(topic.topicsStartTime).toISOString().slice(0, 19).replace('T', ' ') : '',
        topicsDuration: topic.topicsDuration,
        reportDept: transformDeptListForDisplay(topic.reportDept),
        reportList: transformUserListForDisplay(topic.reportList),
        attendanceDept: transformDeptListForDisplay(topic.attendanceDept),
        attendanceList: transformUserListForDisplay(topic.attendanceList)
      }))
    }

    return {
      id: backendData.id,
      name: backendData.name,
      meetingType: backendData.meetingType,
      meetingRoom: backendData.meetingRoom,
      startTime: backendData.startTime ? new Date(backendData.startTime).toISOString().slice(0, 10) : '',
      leaderList: transformUserListForDisplay(backendData.leaderList),
      operatorList: transformUserListForDisplay(backendData.operatorList),
      topicsList: transformTopicsListForDisplay(backendData.topicsList)
    }
  }

  // 数据回填
  useEffect(() => {
    const fetchData = async () => {
      if (id && id !== 'new') {
        try {
          setLoading(true)
          setIsEdit(true)
          const response = await service.getMeetingDetail({ meetingId: id })
          console.log('获取的会议详情:', response)
          if (response) {
            // 转换后端数据为前端格式
            const transformedData = transformDataForDisplay(response)
            console.log('转换后的显示数据:', transformedData)

            setInitValue(transformedData)
            // 设置表单初始值
            actions.setFormState(state => {
              state.values = transformedData
            })
          }
        } catch (error) {
          console.error('获取会议详情失败:', error)
          message.error('获取会议详情失败')
        } finally {
          setLoading(false)
        }
      } else {
        // 新建模式
        setIsEdit(false)
        const defaultValues = {
          topicsList: [] // 默认空的议题列表
        }
        setInitValue(defaultValues)
        actions.setFormState(state => {
          state.values = defaultValues
        })
      }
    }
    fetchData()
  }, [id, actions])
  const expressionScope = {

  }

  // 标记自定义组件为字段组件
  AgendaArrayField.isFieldComponent = true

  // 自定义组件注册给 formily 使用
  UserSelect.isFieldComponent = true

  const components = {
    Input,
    Select,
    DatePicker,
    FormCard,
    SelectDept,
    UserSelect,
    EditableInput,
    AgendaArrayField,
    TimePicker,
    InputNumber,
    FormBlock,
    FormMegaLayout,
    'mega-layout': FormMegaLayout,
  }

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      setLoading(true)

      // 验证表单
      await actions.validate()

      // 获取表单数据
      const formState = await actions.getFormState()
      const values = formState.values
      console.log('表单数据:', values)
      if(!values.topicsList || values.topicsList.length === 0){
        message.error('请添加至少一个议题')
        return
      } else if (values.topicsList && values.topicsList.length > 0) {
        const invalidAgendas = []
        values.topicsList.forEach((agenda, index) => {
          const errors = []
          if (!agenda.reportList || agenda.reportList.length === 0) errors.push('汇报人')
          if (!agenda.topicsStartTime) errors.push('开始时间')
          if (!agenda.topicsDuration) errors.push('汇报时长')
          if (!agenda.topicsName) errors.push('议题名称')
          if (!agenda.reportDept || agenda.reportDept.length === 0) errors.push('汇报单位')
          if (!agenda.attendanceList || agenda.attendanceList.length === 0) errors.push('列席联系人')
          if (!agenda.attendanceDept || agenda.attendanceDept.length === 0) errors.push('列席单位')

          if (errors.length > 0) {
            invalidAgendas.push(`议题${numberToChinese(index + 1)}: ${errors.join('、')}不能为空`)
          }
        })

        if (invalidAgendas.length > 0) {
          message.error(`请完善以下信息：\n${invalidAgendas.join('\n')}`)
          return
        }
      }
      // 数据转换函数：将当前数据结构转换为目标数据结构
      const transformDataForSubmit = (formData) => {
        // 转换用户数据
        const transformUserList = (userList) => {
          if (!Array.isArray(userList)) return []
          return userList.map(user => ({
            uid: user.id,
            name: user.name
          }))
        }

        // 转换部门数据
        const transformDeptList = (deptList) => {
          if (!Array.isArray(deptList)) return []
          return deptList.map(dept => ({
            orgId: dept.orgId,
            orgName: dept.fullname ? dept.fullname.split('-')[0] : '区公司', // 从 fullname 中提取组织名称
            deptId: dept.id,
            deptName: dept.name
          }))
        }

        // 转换议题列表
        const transformTopicsList = (topicsList) => {
          if (!Array.isArray(topicsList)) return []
          return topicsList.map((topic, index) => ({
            topicsName: topic.topicsName,
            topicsTitle: `议题${numberToChinese(index + 1)}`,
            topicsStartTime: new Date(topic.topicsStartTime).getTime(),
            topicsDuration: topic.topicsDuration,
            sequence: index + 1,
            reportDept: transformDeptList(topic.reportDept),
            reportList: transformUserList(topic.reportList),
            attendanceDept: transformDeptList(topic.attendanceDept),
            attendanceList: transformUserList(topic.attendanceList)
          }))
        }

        return {
          name: formData.name,
          meetingDate: new Date(formData.startTime).getTime(),
          meetingType: formData.meetingType,
          meetingRoom: formData.meetingRoom,
          startTime: new Date(formData.startTime).getTime(),
          leaderList: transformUserList(formData.leaderList),
          operatorList: transformUserList(formData.operatorList),
          topicsList: transformTopicsList(formData.topicsList)
        }
      }

      // 使用转换函数处理数据
      const param = transformDataForSubmit(values)
      if(id && id!== 'new'){
        param.id = id
      }
      console.log('转换后的数据:', param)

      await service.saveMeeting(param)
      message.success(isEdit ? '更新成功' : '创建成功')

      // 保存成功后返回列表页
      history.push('/web/MeetManage/meetList')
    } catch (error) {
      console.error('提交失败:', error)
      if (error.name === 'ValidateError') {
        console.log('验证错误详情:', error)
        message.error('请检查表单填写是否完整')
      } else {
        message.error(isEdit ? '更新失败' : '创建失败')
      }
    } finally {
      setLoading(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    history.push('/web/MeetManage/meetList')
  }
  return (
    <div className="meet-manage-container">
      <Spin
        spinning={loading}
        style={{ height: '100vh', overflow: 'hidden', maxHeight: 'initial' }}
      >
        {/* <PageHeader
          title={'创建会议'}
          hasBack={true}
        /> */}
        <div
          className={css`
          overflow: auto;
          height: 100%;
        `}
        >
          <div className="meet-manage-form" style={{ paddingBottom: '80px' }}>
            <div className={css`
                font-size: 18px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 24px;
                text-align: center;
              `}>{isEdit ? '编辑会议' : '创建会议'}</div>
            <div className={css`
                font-size: 16px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 16px;
              `}>会议基本信息</div>
            <SchemaMarkupForm
              schema={schema()}
              components={components}
              actions={actions}
              effects={() => {
                uesEffects()
              }}
              initialValues={initValue}
              expressionScope={{ ...expressionScope }}
              previewPlaceholder='-'
              editable={editable}
            >
            </SchemaMarkupForm>
          </div>

          <FormActions
            onCancel={handleCancel}
            onSubmit={handleSubmit}
            loading={loading}
          />
        </div>
      </Spin>
    </div>
  )
}

export default MeetManage
